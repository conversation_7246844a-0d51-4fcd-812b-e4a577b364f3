<template>
  <div class="fullscreen-email-container">
    <!-- 邮件内容头部 -->
    <div class="fullscreen-header">
      <div class="header-left">
        <h2 class="email-title">{{ email.subject }}</h2>
      </div>
      <div class="header-actions">
        <button class="action-button" @click="printEmail">
          <printer-icon class="icon-small" /> 打印
        </button>
        <button class="action-button" @click="closeFullscreen">
          <x-icon class="icon-small" /> 关闭
        </button>
      </div>
    </div>

    <div class="fullscreen-content">
      <div class="email-meta">
        <div class="sender-info">
          <div class="sender-avatar">
            <user-icon class="icon" />
          </div>
          <div class="sender-details">
            <div class="sender-name">
              {{ email.sender }}
              <span v-if="email.tag">@{{ email.tag }}</span>
            </div>
            <div class="email-date">{{ email.fullDate || email.time }}</div>
          </div>
        </div>
      </div>

      <div class="email-recipients">
        <div class="recipient-group">
          <span class="recipient-label">收件人：</span>
          <div class="recipients-list">
            <span v-for="(recipient, idx) in email.recipients" :key="idx" class="recipient">
              {{ recipient }}
            </span>
          </div>
        </div>

        <div class="recipient-group" v-if="email.ccRecipients && email.ccRecipients.length > 0">
          <span class="recipient-label">抄送：</span>
          <div class="recipients-list">
            <span v-for="(recipient, idx) in email.ccRecipients" :key="idx" class="recipient">
              {{ recipient }}
            </span>
          </div>
        </div>
      </div>

      <div class="email-tags" v-if="email.tags && email.tags.length > 0">
        <span
          v-for="(tagId, tagIndex) in email.tags"
          :key="tagIndex"
          class="email-tag"
          :style="{ backgroundColor: getTagColor(tagId) }"
        >
          {{ getTagName(tagId) }}
        </span>
      </div>

      <div class="email-content">
        <div class="email-body" v-html="email.body"></div>
        <div class="email-signature" v-if="email.signature">
          <div class="signature-content" v-html="email.signature"></div>
        </div>
      </div>

      <div class="email-attachments" v-if="email.attachments && email.attachments.length > 0">
        <div class="attachments-header">
          <paperclip-icon class="icon-small" /> 附件 ({{ email.attachments.length }})
        </div>
        <div class="attachments-list">
          <div
            v-for="(attachment, index) in email.attachments"
            :key="index"
            class="attachment-item"
          >
            <div class="attachment-icon">
              <file-text-icon v-if="isDocumentFile(attachment.name)" class="icon" />
              <image-icon v-else-if="isImageFile(attachment.name)" class="icon" />
              <file-icon v-else class="icon" />
            </div>
            <div class="attachment-info">
              <div class="attachment-name">{{ attachment.name }}</div>
              <div class="attachment-size">{{ formatFileSize(attachment.size) }}</div>
            </div>
            <div class="attachment-actions">
              <download-icon class="icon-small" title="下载" @click.stop="downloadAttachment(attachment)" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  UserIcon,
  PaperclipIcon,
  FileTextIcon,
  ImageIcon,
  FileIcon,
  DownloadIcon,
  PrinterIcon,
  XIcon
} from 'lucide-vue'

export default {
  name: 'FullscreenEmailView',
  components: {
    UserIcon,
    PaperclipIcon,
    FileTextIcon,
    ImageIcon,
    FileIcon,
    DownloadIcon,
    PrinterIcon,
    XIcon
  },
  props: {
    email: {
      type: Object,
      required: true
    },
    tags: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    getTagName(tagId) {
      // 系统标签（1-9）
      const systemTagNames = {
        1: '通知',
        2: '招聘',
        3: '商机',
        4: '报价',
        5: '已更回复',
        6: 'PI',
        7: '订单',
        8: '样品',
        9: '询盘'
      };

      if (tagId >= 1 && tagId <= 9) {
        return systemTagNames[tagId] || '';
      }

      // 自定义标签
      const tag = this.tags.find(t => t.id === tagId);
      return tag ? tag.name : '';
    },
    getTagColor(tagId) {
      // 系统标签颜色
      if (tagId >= 1 && tagId <= 9) {
        return '#e60012';
      }

      // 自定义标签颜色
      const tag = this.tags.find(t => t.id === tagId);
      return tag ? tag.color : '#e60012';
    },
    isDocumentFile(filename) {
      const ext = this.getFileExtension(filename).toLowerCase();
      return ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv', 'json', 'xml', 'html'].includes(ext);
    },
    isImageFile(filename) {
      const ext = this.getFileExtension(filename).toLowerCase();
      return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext);
    },
    getFileExtension(filename) {
      return filename.split('.').pop() || '';
    },
    formatFileSize(bytes) {
      if (!bytes || bytes === 0) return '0 KB';

      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));

      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    downloadAttachment(attachment) {
      // 模拟下载附件
      this.$emit('download-attachment', attachment);
    },
    printEmail() {
      this.$emit('print', this.email);
    },
    closeFullscreen() {
      this.$emit('close');
    }
  }
}
</script>

<style scoped>
.fullscreen-email-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.fullscreen-header {
  padding: 16px;
  border-bottom: 1px solid #e6e9ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  position: relative;
  z-index: 50 !important; /* 设置为比tab-navigation低的z-index */
  margin-top: 40px; /* 添加顶部边距，确保不会覆盖tab导航 */
}

.email-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background-color: #f5f7fa;
  border: 1px solid #e6e9ed;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.action-button:hover {
  background-color: #e6e9ed;
}

.icon-small {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.fullscreen-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.email-meta {
  margin-bottom: 16px;
}

.sender-info {
  display: flex;
  align-items: center;
}

.sender-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.icon {
  width: 20px;
  height: 20px;
  color: #666;
}

.sender-details {
  flex: 1;
}

.sender-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.email-date {
  font-size: 12px;
  color: #666;
}

.email-recipients {
  margin-bottom: 16px;
}

.recipient-group {
  display: flex;
  margin-bottom: 8px;
}

.recipient-label {
  width: 60px;
  flex-shrink: 0;
  color: #666;
}

.recipients-list {
  flex: 1;
}

.recipient {
  margin-right: 8px;
}

.email-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.email-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
}

.email-content {
  margin-bottom: 24px;
}

.email-body {
  line-height: 1.6;
}

.email-signature {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e6e9ed;
  color: #666;
}

.email-attachments {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e6e9ed;
}

.attachments-header {
  font-weight: 500;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.attachments-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e6e9ed;
  border-radius: 4px;
  width: 100%;
  max-width: 300px;
}

.attachment-icon {
  margin-right: 12px;
}

.attachment-info {
  flex: 1;
}

.attachment-name {
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.attachment-size {
  font-size: 12px;
  color: #666;
}

.attachment-actions {
  display: flex;
  gap: 8px;
}

/* 确保图标正确显示 */
.icon, .icon-small {
  width: 16px;
  height: 16px;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
  margin-right: 4px;
}

::v-deep svg {
  width: 16px;
  height: 16px;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}

/* 移除顶部菜单样式 */

@media print {
  .nav-container,
  .fullscreen-header {
    display: none;
  }

  .fullscreen-email-container {
    box-shadow: none;
  }
}

/* 确保全屏视图不会覆盖顶部导航栏和标签导航 */
.fullscreen-email-container {
  margin-top: 0;
  padding-top: 0;
  position: relative;
  z-index: 10 !important; /* 确保不会覆盖tab导航 */
}
</style>
