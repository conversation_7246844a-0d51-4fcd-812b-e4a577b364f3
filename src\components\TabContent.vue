<template>
  <div class="tab-content" :class="{ 'fullscreen-mode': isFullscreenMode }">
    <!-- 根据标签页类型显示不同的内容 -->
    <component
      :is="contentComponent"
      v-if="contentComponent"
      :email="tab.email"
      :tags="tags"
      @close="closeTab"
      @save-success="handleSaveSuccess"
      @cancel="closeTab"
    />

    <!-- 如果没有匹配的组件，显示默认内容 -->
    <div v-else class="default-content">
      <h3>{{ tab.title }}</h3>
      <p>内容正在加载中...</p>
    </div>
  </div>
</template>

<script>
import NewClueForm from '@/views/crm/email/components/NewClueForm.vue'
import FullscreenEmailView from '@/views/crm/email/components/FullscreenEmailView.vue'

export default {
  name: 'TabContent',
  components: {
    NewClueForm,
    FullscreenEmailView
  },
  props: {
    tab: {
      type: Object,
      required: true
    },
    tags: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    contentComponent() {
      // 根据标签页类型返回对应的组件
      const typeComponentMap = {
        'clue': 'NewClueForm',
        'fullscreen': 'FullscreenEmailView'
      };

      return typeComponentMap[this.tab.type] || null;
    },

    // 是否为全屏模式
    isFullscreenMode() {
      return this.tab.isFullscreen === true;
    }
  },
  methods: {
    closeTab() {
      this.$emit('close', this.tab.id);
    },

    handleSaveSuccess(data) {
      this.$emit('save-success', {
        tabId: this.tab.id,
        data: data
      });

      // 保存成功后关闭标签页
      this.closeTab();
    }
  }
}
</script>

<style scoped>
.tab-content {
  height: 100%;
  overflow: auto;
  background-color: #fff;
  position: relative;
  z-index: 200 !important; /* 设置为比tab-navigation低但比其他元素高的z-index */

  &.fullscreen-mode {
    /* 全屏模式样式 */
    display: flex;
    flex-direction: column;
    padding-top: 40px; /* 添加内边距，为tab导航留出空间 */
  }
}

.default-content {
  padding: 24px;
  text-align: center;
  color: #606266;
}
</style>
