<template>
  <el-container>
    <el-header class="nav-container">
      <navbar
        :menus="menus"
        :title="title"
        @select="menuSelect"
      />
    </el-header>
    <!-- 添加Tab导航区域，仅在邮件页面显示 -->
    <tab-navigation
      v-if="showTabNavigation"
      :active-tab="currentTab"
      :tabs="tabPages"
      @tab-change="handleTabChange"
      @tab-close="handleTabClose"
    />

    <!-- 标签页内容区域 -->
    <div v-if="showTabContent" class="tab-content-container">
      <tab-content
        :tab="currentTabData"
        :tags="emailTags"
        @close="handleTabClose"
        @save-success="handleTabSaveSuccess"
      />
    </div>
    <wk-container
      :menu="sideMenus"
      :header-obj="headerCellObj"
    >
      <el-main style="padding-top: 24px;">
        <app-main />
      </el-main>
    </wk-container>
  </el-container>
</template>

<script>
import { Navbar, AppMain } from './components'
import WkContainer from './components/WkContainer'
import TabNavigation from '@/components/TabNavigation'
import TabContent from '@/components/TabContent'

import { mapGetters } from 'vuex'
import { getNavMenus } from './components/utils'
import path from 'path'

export default {
  name: 'OaLayout',

  components: {
    Navbar,
    AppMain,
    WkContainer,
    TabNavigation,
    TabContent
  },

  data() {
    return {
      sideChildRouter: null, // 包含child的路由对象
      sideMenus: [],
      currentTab: 'home', // 当前激活的标签，默认为首页
      tabPages: [
        { id: 'home', title: '首页', closable: false },
        { id: 'email', title: '邮件页', closable: false }
      ]
    }
  },

  computed: {
    ...mapGetters([
      'oa', 'oaRouters',
      'crmRouters', 'messageNum']),
    menus() {
      const { query } = this.$route
      if (query.module === 'crm') {
        const navs = getNavMenus(this.crmRouters || [], '/crm')
        const messageObj = navs.find(item => item.path.includes('/message'))
        if (messageObj) {
          messageObj.num = this.messageNum.totalNum
        }
        return navs
      } else if (query.module === 'bi' ||
      query.module === 'manage') {
        return []
      }
      const navs = getNavMenus(this.oaRouters || [], '/oa')
      return navs
    },

    title() {
      const { query } = this.$route
      if (query.module === 'crm') {
        return 'CRM'
      } else if (query.module === 'bi') {
        return 'BI'
      } else if (query.module === 'manage') {
        return '系统设置'
      }
      return 'OA'
    },
    headerCellObj() {
      const { path } = this.$route;
      if (path.includes('oa/task')) {
        return {
          icon: 'wk wk-task',
          label: '任务',
          des: '任务管理'
        }
      } else if (path.includes('oa/examine')) {
        return {
          icon: 'wk wk-approve',
          label: '审批',
          des: '审批管理'
        }
      }else if (path.includes('oa/email') || path.includes('crm/email')) {
        return {
          icon: 'wk wk-email',
          label: '邮箱',
          des: '邮箱管理'
        }
      } else if (path.includes('oa/log')) {
        return {
          icon: 'wk wk-log',
          label: '日志',
          des: '日志管理'
        }
      }
      return null
    },

    // 是否显示Tab导航
    showTabNavigation() {
      // 在邮件页面或有自定义标签页时显示Tab导航（支持OA和CRM模块）
      const isEmailPage = this.$route.path.includes('oa/email') ||
                          this.$route.path.includes('crm/email');
      const hasCustomTabs = this.tabPages.length > 2; // 超过默认的首页和邮件页
      return isEmailPage || hasCustomTabs;
    },

    // 是否显示标签页内容
    showTabContent() {
      // 当前标签页不是首页或邮件页时显示标签页内容
      return this.currentTab !== 'home' && this.currentTab !== 'email';
    },

    // 当前标签页数据
    currentTabData() {
      return this.tabPages.find(tab => tab.id === this.currentTab) || {};
    },

    // 邮件标签数据
    emailTags() {
      // 这里可以从Vuex或其他地方获取邮件标签数据
      return [
        { id: 1, name: '通知', color: '#e60012' },
        { id: 2, name: '招聘', color: '#e60012' },
        { id: 3, name: '商机', color: '#e60012' },
        { id: 4, name: '报价', color: '#e60012' },
        { id: 5, name: '已更回复', color: '#e60012' },
        { id: 6, name: 'PI', color: '#e60012' },
        { id: 7, name: '订单', color: '#e60012' },
        { id: 8, name: '样品', color: '#e60012' },
        { id: 9, name: '询盘', color: '#e60012' }
      ];
    }
  },

  watch: {
    $route: {
      handler(route) {
        // 根据路由更新当前激活的标签
        if (route.path === '/' || route.path.startsWith('/home')) {
          // 只有在没有自定义标签页激活时才切换到首页
          if (!this.currentTab.startsWith('clue-') &&
              !this.currentTab.startsWith('fullscreen-') &&
              !this.currentTab.startsWith('order-')) {
            this.currentTab = 'home'
          }
        } else if (route.path.includes('/oa/email') ||
                   route.path.includes('/crm/email')) {
          // 只有在没有自定义标签页激活时才切换到邮件页
          if (!this.currentTab.startsWith('clue-') &&
              !this.currentTab.startsWith('fullscreen-') &&
              !this.currentTab.startsWith('order-')) {
            this.currentTab = 'email'
          }
        }
      },
      immediate: true
    }
  },

  created() {
  },

  mounted() {
    this.initEventListeners()
  },

  methods: {
    /**
     * 菜单选择
     */
    menuSelect(menu, isOa) {
      if (isOa) {
        const router = this.oaRouters[menu.index]
        if (router && router.children && router.children.length > 1) {
          this.sideChildRouter = router
          this.sideMenus = this.getSideMenus(router.path, router.children)
        } else {
          this.sideChildRouter = null
          this.sideMenus = []
        }
      }
    },

    /**
     * 获取siderMenus
     */
    getSideMenus(mainPath, children) {
      const { module } = this.$route.query
      const query = module ? `?module=${module}` : ''
      const sideMenus = []
      children.forEach(item => {
        let auth = true
        if (item.permissions) {
          auth = this.$auth(item.permissions.join('.'))
        }

        if (item.permissionList) {
          let hasAuth = false
          for (let index = 0; index < item.permissionList.length; index++) {
            const element = item.permissionList[index]
            const childAuth = this.$auth(element.join('.'))
            if (childAuth) {
              hasAuth = childAuth
              break
            }
          }
          if (!hasAuth) {
            auth = hasAuth
          }
        }

        if (!item.hidden && auth) {
          sideMenus.push({
            ...item,
            path: path.resolve(mainPath, item.path) + query
          })
        }
      })
      return sideMenus
    },

    navClick() {},
    /**
     * 菜单钢鞭
     */
    handleSelect() {
      this.$store.dispatch('GetMessageNum')
    },

    /**
     * 处理Tab切换
     */
    handleTabChange(tabId) {
      this.currentTab = tabId

      // 根据tab切换路由
      if (tabId === 'home') {
        this.$router.push('/')
      } else if (tabId === 'email') {
        // 根据当前路径判断是OA还是CRM模块
        if (this.$route.path.includes('/crm/')) {
          this.$router.push('/crm/email')
        } else {
          this.$router.push('/oa/email')
        }
      } else if (tabId.startsWith('clue-') ||
                 tabId.startsWith('fullscreen-') ||
                 tabId.startsWith('order-')) {
        // 处理自定义标签页，不需要切换路由，保持当前页面
        // 如果当前不在邮件页面，则跳转到邮件页面以确保有正确的上下文
        if (!this.$route.path.includes('/email')) {
          if (this.$route.path.includes('/crm/')) {
            this.$router.push('/crm/email')
          } else {
            this.$router.push('/oa/email')
          }
        }
      }
    },

    /**
     * 处理Tab关闭
     */
    handleTabClose({ tabId, index }) {
      // 获取要关闭的标签页
      const closingTab = this.tabPages.find(tab => tab.id === tabId);
      const isFullscreen = closingTab && closingTab.isFullscreen;

      // 从标签页数组中移除该标签页
      this.tabPages = this.tabPages.filter(tab => tab.id !== tabId);

      // 如果关闭的是当前激活的标签页，则切换到前一个标签页
      if (this.currentTab === tabId) {
        // 重新计算索引，因为数组已经变化
        const newActiveIndex = Math.min(index, this.tabPages.length - 1);
        const newActiveTab = this.tabPages[newActiveIndex];

        if (newActiveTab) {
          this.currentTab = newActiveTab.id;
          // 如果切换到了首页或邮件页，需要相应地切换路由
          this.handleTabChange(this.currentTab);
        }

        // 如果关闭的是全屏标签页，移除全屏样式类
        if (isFullscreen) {
          this.$nextTick(() => {
            const container = document.querySelector('.tab-content-container');
            if (container) {
              container.classList.remove('fullscreen-mode');
            }
          });
        }
      }

      // 强制更新视图以确保tab导航状态正确
      this.$forceUpdate();
    },

    /**
     * 添加新标签页
     */
    addTab(tab) {
      // 检查标签页是否已存在
      const existingTab = this.tabPages.find(t => t.id === tab.id);
      if (!existingTab) {
        this.tabPages.push(tab);
      } else {
        // 如果标签页已存在，更新其内容
        const index = this.tabPages.findIndex(t => t.id === tab.id);
        this.tabPages.splice(index, 1, { ...existingTab, ...tab });
      }

      // 激活新标签页
      this.currentTab = tab.id;
      console.log("当前tab页++++++++++",tab);

      // 强制更新视图以确保tab导航显示
      this.$forceUpdate();

      // 如果是全屏模式，添加全屏样式类
      if (tab.isFullscreen) {
        this.$nextTick(() => {
          // 添加全屏样式
          const container = document.querySelector('.tab-content-container');
          if (container) {
            container.classList.add('fullscreen-mode');
          }
        });
      } else {
        this.$nextTick(() => {
          const container = document.querySelector('.tab-content-container');
          if (container) {
            container.classList.remove('fullscreen-mode');
          }
        });
      }
    },

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
      // 监听添加标签页事件
      this.$bus.on('add-tab', (tab) => {
        this.addTab(tab);
      });
    },

    /**
     * 处理标签页保存成功
     */
    handleTabSaveSuccess({ tabId, data }) {
      console.log('标签页保存成功:', tabId, data);

      // 这里可以根据不同的标签页类型处理不同的保存逻辑
      if (tabId.startsWith('clue-')) {
        // 处理线索保存成功
        this.$message.success('线索创建成功');
      } else if (tabId.startsWith('order-')) {
        // 处理订单保存成功
        this.$message.success('订单创建成功');
      }

      // 关闭标签页
      this.tabPages = this.tabPages.filter(tab => tab.id !== tabId);

      // 切换到邮件标签页
      this.currentTab = 'email';
      // 根据当前路径判断是OA还是CRM模块
      if (this.$route.path.includes('/crm/')) {
        this.$router.push('/crm/email');
      } else {
        this.$router.push('/oa/email');
      }
    }
  },

  beforeDestroy() {
    // 清理事件监听器
    this.$bus.off('add-tab');
  }
}
</script>

<style lang="scss" scoped>
@import "./components/style";
@import "./styles/common.scss";

.el-container {
  height: 100%;
  min-height: 0;
}

.nav-container {
  min-width: 1200px;
  padding: 0;
  box-shadow: 0 1px 2px #dbdbdb;
}

.el-main {
  padding-right: 0;
  padding-left: 0;
}

/* Tab导航样式 */
:deep(.tab-navigation) {
  min-width: 1200px;
  z-index: 1000 !important; /* 使用更高的z-index并添加!important确保优先级 */
  position: relative; /* 确保z-index生效 */
}

/* 标签页内容容器样式 */
.tab-content-container {
  position: absolute;
  top: 100px; /* 顶部导航栏高度 + Tab导航高度 */
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 500 !important; /* 设置为比tab-navigation低但比其他元素高的z-index */
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  /* 确保不会覆盖顶部导航栏和Tab导航 */
  &:not(.fullscreen-mode) {
    top: 100px; /* 顶部导航栏高度 + Tab导航高度 */
  }

  &.fullscreen-mode {
    top: 100px; /* 保持与Tab导航一致，确保不会覆盖Tab导航 */
    padding-top: 40px; /* 添加内边距，为tab导航留出空间 */
  }
}
</style>
